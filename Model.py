#!/usr/bin/env python3
"""
Quotex Trading Model
Comprehensive trading bot with Quotex integration using quotexpy
"""

import sys
import os
import time
import asyncio
import threading
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Import quotexpy for Quotex integration
try:
    from quotexpy import Quotex
    from quotexpy.constants import codes_asset
    QUOTEX_AVAILABLE = True
except ImportError:
    print("❌ quotexpy not found. Please install: pip install quotexpy")
    QUOTEX_AVAILABLE = False

# Import existing modules
from utils import print_colored, print_header, format_price, fetch_live_candles, get_timeframe_time_info
from strategy_engine import StrategyEngine
from config import STRATEGY_CONFIG, OANDA_CONFIG, TIMEFRAME_CONFIG

# Quotex credentials and URLs (Official Quotex URL)
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"
QUOTEX_LIVE_URL = "https://market-qx.pro/en/trade"  # Official Quotex URL
QUOTEX_DEMO_URL = "https://market-qx.pro/en/demo-trade"

# Quotex supported assets (from quotexpy constants)
QUOTEX_OTC_PAIRS = [
    "EURUSD_otc", "GBPUSD_otc", "USDJPY_otc", "AUDUSD_otc", "USDCAD_otc", "USDCHF_otc",
    "AUDCAD_otc", "AUDCHF_otc", "AUDJPY_otc", "CADJPY_otc", "EURCHF_otc", "EURGBP_otc",
    "EURJPY_otc", "GBPAUD_otc", "GBPJPY_otc", "NZDJPY_otc", "NZDUSD_otc", "XAGUSD_otc",
    "XAUUSD_otc", "UKBrent_otc", "USCrude_otc", "AXP_otc", "BA_otc", "FB_otc", "INTC_otc",
    "JNJ_otc", "MCD_otc", "MSFT_otc", "PFE_otc"
]

QUOTEX_LIVE_PAIRS = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURAUD",
    "EURCAD", "EURCHF", "EURGBP", "EURJPY", "EURSGD", "GBPAUD", "GBPCAD", "GBPCHF",
    "GBPJPY", "XAGUSD", "XAUUSD", "DJIUSD", "F40EUR", "FTSGBP", "GEREUR", "HSIHKD",
    "IBXEUR", "IT4EUR", "JPXJPY", "NDXUSD", "SPXUSD", "STXEUR"
]

# Available timeframes for Quotex
QUOTEX_TIMEFRAMES = {
    "1": {"name": "1 Minute", "seconds": 60},
    "2": {"name": "2 Minutes", "seconds": 120},
    "5": {"name": "5 Minutes", "seconds": 300},
    "10": {"name": "10 Minutes", "seconds": 600},
    "15": {"name": "15 Minutes", "seconds": 900},
    "30": {"name": "30 Minutes", "seconds": 1800},
    "60": {"name": "1 Hour", "seconds": 3600}
}

# Global Quotex client
quotex_client = None

# Data caching for performance optimization
data_cache = {}
cache_timeout = 30  # Cache data for 30 seconds
last_cache_time = {}

def show_menu():
    """Display the main menu"""
    print_header("🚀 QUOTEX TRADING BOT SYSTEM")
    print_colored("Choose an option:", "SKY_BLUE", bold=True)
    print()
    print_colored("1. 📊 Practice (Signal display only)", "MENU_GREEN", bold=True)
    print_colored("2. 🎯 Quotex Demo (Demo trading)", "WARNING", bold=True)
    print_colored("3. 💰 Quotex Live (Live trading)", "SKY_BLUE", bold=True)
    print_colored("4. 💳 Check Quotex Balance", "INFO", bold=True)
    print_colored("5. ❌ Exit", "ERROR", bold=True)
    print()

async def check_quotex_balance():
    """Check and display Quotex account balance"""
    print_header("💳 QUOTEX BALANCE CHECK")

    if not QUOTEX_AVAILABLE:
        print_colored("❌ quotexpy not installed. Run: pip install quotexpy", "ERROR")
        return

    try:
        print_colored("🔄 Connecting to Quotex to check balance...", "SKY_BLUE", bold=True)

        # Connect to demo account first
        demo_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD, headless=True)
        demo_connected = await demo_client.connect()

        if demo_connected:
            demo_client.change_account("PRACTICE")
            await demo_client.get_instruments()
            demo_balance = await demo_client.get_balance()
            print_colored(f"💰 Demo Balance: ${demo_balance:.2f}", "LIST_GREEN" if demo_balance > 0 else "WARNING")
            demo_client.close()
        else:
            print_colored("❌ Failed to connect to demo account", "HOLD")

        # Connect to live account
        live_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD, headless=True)
        live_connected = await live_client.connect()

        if live_connected:
            live_client.change_account("REAL")
            await live_client.get_instruments()
            live_balance = await live_client.get_balance()
            print_colored(f"💰 Live Balance: ${live_balance:.2f}", "LIST_GREEN" if live_balance > 0 else "WARNING")
            live_client.close()
        else:
            print_colored("❌ Failed to connect to live account", "HOLD")

    except Exception as e:
        print_colored(f"❌ Balance check failed: Connection error", "HOLD")

async def connect_to_quotex(account_type="PRACTICE"):
    """Connect to Quotex with enhanced retry mechanism"""
    global quotex_client

    if not QUOTEX_AVAILABLE:
        print_colored("❌ quotexpy not installed. Run: pip install quotexpy", "ERROR")
        return False

    print_colored(f"🔄 Attempting to connect to Quotex ({account_type} mode)...", "SKY_BLUE", bold=True)
    print_colored(f"📍 Using URL: {QUOTEX_LIVE_URL}", "LIST_GREEN")

    # Try multiple connection approaches
    connection_methods = [
        {"headless": True, "name": "Headless Mode"},
        {"headless": False, "name": "Browser Mode"},
    ]
    
    for method in connection_methods:
        try:
            print_colored(f"🔗 Trying {method['name']}...", "LIST_GREEN")

            # Create new client instance
            quotex_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD, headless=method["headless"])

            # Try to connect with timeout
            connected = await asyncio.wait_for(quotex_client.connect(), timeout=30.0)

            if connected:
                print_colored("✅ Connected to Quotex successfully!", "LIST_GREEN", bold=True)

                # Set account type
                quotex_client.change_account(account_type)
                print_colored(f"🔄 Switched to {account_type} account", "LIST_GREEN")

                # Get instruments with timeout
                try:
                    await asyncio.wait_for(quotex_client.get_instruments(), timeout=20.0)
                    print_colored("📊 Loaded trading instruments", "LIST_GREEN")
                except asyncio.TimeoutError:
                    print_colored("⚠️  Timeout loading instruments, but connection is active", "WARNING")

                # Test connection by checking balance
                try:
                    balance = await asyncio.wait_for(quotex_client.get_balance(), timeout=15.0)
                    print_colored(f"💰 Account balance: ${balance:.2f}", "LIST_GREEN")
                    return True
                except Exception as e:
                    print_colored(f"⚠️  Could not get balance: {str(e)}", "WARNING")
                    return True  # Connection is still valid
            else:
                print_colored(f"❌ {method['name']} failed", "HOLD")
                if quotex_client:
                    try:
                        quotex_client.close()
                    except:
                        pass
                    quotex_client = None

        except asyncio.TimeoutError:
            print_colored(f"⏰ {method['name']} timed out", "HOLD")
            if quotex_client:
                try:
                    quotex_client.close()
                except:
                    pass
                quotex_client = None
        except Exception as e:
            print_colored(f"❌ {method['name']} error: Connection failed", "HOLD")
            if quotex_client:
                try:
                    quotex_client.close()
                except:
                    pass
                quotex_client = None

    print_colored("❌ Could not connect to Quotex", "HOLD", bold=True)
    print_colored("💡 Please check your internet connection and try again", "WARNING")
    return False

async def check_balance():
    """Check current account balance"""
    if not quotex_client:
        return 0

    try:
        balance = await quotex_client.get_balance()
        return balance if balance else 0
    except Exception as e:
        print_colored(f"❌ Error checking balance: {str(e)}", "ERROR")
        return 0

def display_pairs_in_columns(pairs, title, columns=4, start_index=0):
    """Display trading pairs in specified number of columns"""
    print_colored(f"\n{title}", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")

    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{start_index+i+j+1:2d}. {pair:<18}"
        print_colored(formatted_row, "LIST_GREEN")

    print_colored("=" * 80, "SKY_BLUE")

def select_trading_pairs():
    """Select multiple trading pairs from available options"""
    print_header("💱 ASSET SELECTION")

    # Display OTC pairs
    display_pairs_in_columns(QUOTEX_OTC_PAIRS, "📊 OTC Pairs (24/7 Available):", start_index=0)

    # Display Live pairs with correct numbering
    otc_count = len(QUOTEX_OTC_PAIRS)
    display_pairs_in_columns(QUOTEX_LIVE_PAIRS, "🌍 Live Pairs (Market Hours):", columns=4, start_index=otc_count)

    total_pairs = len(QUOTEX_OTC_PAIRS) + len(QUOTEX_LIVE_PAIRS)
    all_pairs = QUOTEX_OTC_PAIRS + QUOTEX_LIVE_PAIRS

    print_colored(f"\n🔸 Select pairs (1,2,3 or 'all' for all pairs):", "DARK_ORANGE", bold=True)

    while True:
        try:
            selection = input(f"\nSelect assets (1-{total_pairs}): ").strip().lower()
            if not selection:
                return None

            if selection == 'all':
                selected_pairs = all_pairs.copy()
                break

            # Parse selection
            selected_pairs = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                if '-' in part:
                    # Range selection (e.g., 1-4)
                    start, end = map(int, part.split('-'))
                    for i in range(start-1, min(end, total_pairs)):
                        if i >= 0:
                            selected_pairs.append(all_pairs[i])
                else:
                    # Single selection
                    num = int(part)
                    if 1 <= num <= total_pairs:
                        selected_pairs.append(all_pairs[num-1])
                    else:
                        raise ValueError(f"Invalid asset number: {num}")

            # Remove duplicates while preserving order
            selected_pairs = list(dict.fromkeys(selected_pairs))

            if selected_pairs:
                break
            else:
                print_colored("❌ Please select at least one asset", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected pairs
    print_colored(f"\n✅ Selected {len(selected_pairs)} assets:", "LIST_GREEN", bold=True)
    for pair in selected_pairs:
        print_colored(f"   • {pair}", "LIST_GREEN")

    return selected_pairs

def select_timeframe():
    """Select trading timeframe"""
    print_header("⏰ TIMEFRAME SELECTION")

    print_colored("Available timeframes:", "SKY_BLUE", bold=True)
    for key, info in QUOTEX_TIMEFRAMES.items():
        print_colored(f"{key}. {info['name']}", "LIST_GREEN")

    print_colored("\n🔸 Select timeframe (1-7):", "DARK_ORANGE", bold=True)

    while True:
        try:
            choice = input("\nSelect timeframe (1-7): ").strip()
            if choice in QUOTEX_TIMEFRAMES:
                selected = QUOTEX_TIMEFRAMES[choice]
                print_colored(f"✅ Selected: {selected['name']}", "LIST_GREEN")
                return int(choice) * 60  # Return duration in seconds
            else:
                print_colored("❌ Please enter a valid timeframe number", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_trade_amount():
    """Select trade amount"""
    print_header("💰 TRADE AMOUNT SELECTION")

    print_colored("Available trade amounts:", "SKY_BLUE", bold=True)
    amounts = ["1", "2", "5", "10", "20", "50", "100"]
    for i, amount in enumerate(amounts, 1):
        print_colored(f"{i}. ${amount}", "LIST_GREEN")

    print_colored("\n🔸 Select trade amount (1-7 or enter custom amount):", "DARK_ORANGE", bold=True)
    print_colored("💡 Minimum: $1, Recommended: $1-$10 for demo", "WARNING")

    while True:
        try:
            amount_input = input("\nTrade amount (1-7 or custom $): ").strip()
            if not amount_input:
                return None

            # Check if it's a menu selection (1-7)
            if amount_input.isdigit() and 1 <= int(amount_input) <= 7:
                amount = float(amounts[int(amount_input) - 1])
            else:
                # Try to parse as custom amount
                amount = float(amount_input.replace('$', ''))

            if amount < 1:
                print_colored("❌ Minimum trade amount is $1", "ERROR")
                continue
            elif amount > 1000:
                print_colored("❌ Maximum trade amount is $1000", "ERROR")
                continue

            print_colored(f"✅ Trade amount: ${amount}", "LIST_GREEN")
            return amount

        except ValueError:
            print_colored("❌ Please enter a valid number or selection", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_strategies():
    """Select trading strategies"""
    print_header("🎯 STRATEGY SELECTION")

    print_colored("Available strategies:", "SKY_BLUE", bold=True)
    strategies = list(STRATEGY_CONFIG.keys())

    # Display strategies in two columns
    for i in range(0, len(strategies), 2):
        left_strategy = strategies[i]
        left_info = STRATEGY_CONFIG[left_strategy]
        left_text = f"{i+1}. {left_strategy}: {left_info['name']}"

        if i + 1 < len(strategies):
            right_strategy = strategies[i + 1]
            right_info = STRATEGY_CONFIG[right_strategy]
            right_text = f"{i+2}. {right_strategy}: {right_info['name']}"
            print_colored(f"{left_text:<40} {right_text}", "LIST_GREEN")
        else:
            print_colored(left_text, "LIST_GREEN")

    print_colored("\n🔸 Select strategies (1,2,3 or 'all' for all strategies):", "DARK_ORANGE", bold=True)

    while True:
        try:
            selection = input("\nEnter strategy numbers: ").strip().lower()

            if selection == 'all':
                selected_strategies = strategies
                break

            # Parse selection
            selected_strategies = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                num = int(part)
                if 1 <= num <= len(strategies):
                    selected_strategies.append(strategies[num-1])
                else:
                    raise ValueError(f"Invalid strategy number: {num}")

            if selected_strategies:
                break
            else:
                print_colored("❌ Please select at least one strategy", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected strategies
    print_colored(f"\n✅ Selected {len(selected_strategies)} strategies:", "LIST_GREEN", bold=True)
    for strategy_id in selected_strategies:
        strategy_info = STRATEGY_CONFIG[strategy_id]
        print_colored(f"   • {strategy_id}: {strategy_info['name']}", "LIST_GREEN")

    return selected_strategies

def calculate_next_candle_time(duration_seconds):
    """Calculate time remaining until next candle opens"""
    now = datetime.now()

    if duration_seconds == 60:  # 1 minute
        next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)
    elif duration_seconds == 120:  # 2 minutes
        minutes_mod = now.minute % 2
        next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=2-minutes_mod)
    elif duration_seconds == 300:  # 5 minutes
        minutes_mod = now.minute % 5
        next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=5-minutes_mod)
    elif duration_seconds == 600:  # 10 minutes
        minutes_mod = now.minute % 10
        next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=10-minutes_mod)
    elif duration_seconds == 900:  # 15 minutes
        minutes_mod = now.minute % 15
        next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=15-minutes_mod)
    elif duration_seconds == 1800:  # 30 minutes
        minutes_mod = now.minute % 30
        next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=30-minutes_mod)
    elif duration_seconds == 3600:  # 1 hour
        next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
    else:
        next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)

    time_to_next = (next_candle - now).total_seconds()
    return max(0, time_to_next), next_candle

async def run_trading_bot(account_type, is_practice_only=False):
    """Main trading bot execution"""
    print_header(f"🚀 QUOTEX TRADING BOT - {account_type.upper()} MODE")

    # Connect to Quotex
    if not is_practice_only:
        connected = await connect_to_quotex(account_type)
        if not connected:
            print_colored("❌ Cannot proceed without Quotex connection", "ERROR")
            return

        # Check balance
        balance = await check_balance()
        print_colored(f"💰 Current balance: ${balance:.2f}", "LIST_GREEN" if balance > 0 else "ERROR")

        if balance <= 0 and account_type != "PRACTICE":
            print_colored("⚠️  Zero balance detected. Switching to practice mode...", "WARNING")
            quotex_client.change_account("PRACTICE")
            account_type = "PRACTICE"

    # Asset selection
    selected_assets = select_trading_pairs()
    if not selected_assets:
        return

    # Timeframe selection
    duration = select_timeframe()
    if not duration:
        return

    # Trade amount selection
    if not is_practice_only:
        trade_amount = select_trade_amount()
        if not trade_amount:
            return
    else:
        trade_amount = 1  # Default for practice mode

    # Strategy selection
    selected_strategies = select_strategies()
    if not selected_strategies:
        return

    # Initialize strategy engine
    strategy_engine = StrategyEngine()

    # Display configuration in the exact format requested
    print()
    print_colored("📋 Trading Configuration:", "SKY_BLUE", bold=True)
    pairs_str = ", ".join(selected_assets[:3]) + ("..." if len(selected_assets) > 3 else "")
    timeframe_name = QUOTEX_TIMEFRAMES.get(str(duration//60), {"name": f"{duration//60}m"})["name"]
    strategies_str = ", ".join(selected_strategies[:2]) + ("..." if len(selected_strategies) > 2 else "")
    account_mode = "Practice" if is_practice_only else account_type.title()

    print_colored(f"   Pairs: {pairs_str}", "LIST_GREEN")
    print_colored(f"   Timeframe: {timeframe_name}", "LIST_GREEN")
    print_colored(f"   Strategies: {strategies_str}", "LIST_GREEN")
    print_colored(f"   Account: {account_mode}", "LIST_GREEN")
    print_colored(f"   Amount: ${trade_amount}", "LIST_GREEN")
    print()

    print_colored("🎯 Starting trading bot...", "SKY_BLUE", bold=True)
    print_colored(f"📊 Monitoring {len(selected_assets)} pair(s) with {len(selected_strategies)} strategy(ies)", "LIST_GREEN")

    # Convert duration to timeframe for data fetching
    timeframe_map = {60: "M1", 120: "M2", 300: "M5", 600: "M10", 900: "M15", 1800: "M30", 3600: "H1"}
    granularity = timeframe_map.get(duration, "M1")

    try:
        last_signal_time = None

        while True:
            # Calculate time to next candle
            time_to_next, next_candle_time = calculate_next_candle_time(duration)

            # Generate signals 2 seconds before next candle
            if time_to_next <= 2 and time_to_next > 0:
                # Avoid duplicate signals for the same candle
                if last_signal_time != next_candle_time:
                    # Display next scan time
                    print_colored(f"⏳ Next scan in {int(time_to_next)} seconds at {next_candle_time.strftime('%H:%M:%S')}", "SKY_BLUE")

                    signal_start_time = datetime.now()

                    # Display market scan header
                    print_colored("=" * 80, "SKY_BLUE")
                    print_colored(f"                      📊 MARKET SCAN - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", "SKY_BLUE", bold=True)
                    print_colored("=" * 80, "SKY_BLUE")

                    # Display table header
                    print_colored("=" * 120, "SKY_BLUE")
                    header_line = (
                        f"💱 {'PAIR':<15} | 📅 {'DATE':<15} | 🕐 {'TIME':<12} | 📈📉 {'DIRECTION':<12} | "
                        f"🎯 {'CONFIDENCE':<12} | 💰 {'PRICE':<12} | 🔧 {'STRATEGY':<15}"
                    )
                    print_colored(header_line, "TABLE_HEADER", bold=True)
                    print_colored("=" * 120, "SKY_BLUE")

                    # Simulate signal generation for demo
                    signals_found = False
                    for asset in selected_assets:
                        # Mock signal generation
                        signal = np.random.choice(["call", "put", "hold"], p=[0.3, 0.3, 0.4])
                        confidence = np.random.uniform(0.6, 0.9) if signal != "hold" else 0.0
                        price = 1.16882 + np.random.uniform(-0.001, 0.001)
                        strategy = selected_strategies[0] if selected_strategies else "S1"

                        # Determine signal color and display
                        if signal == "call":
                            signal_display = "🟢 CALL"
                            signal_color = "LIST_GREEN"
                            signals_found = True
                        elif signal == "put":
                            signal_display = "🔴 PUT"
                            signal_color = "LIST_GREEN"
                            signals_found = True
                        else:
                            signal_display = "❌ No Signal"
                            signal_color = "HOLD"

                        # Format confidence
                        conf_display = f"🎯 {confidence*100:.1f}%" if confidence > 0 else "🎯 -"

                        # Display signal row with next candle time
                        strategy_name = STRATEGY_CONFIG.get(strategy, {}).get('name', strategy)
                        row_line = (
                            f"💱 {asset:<15} | 📅 {next_candle_time.strftime('%Y-%m-%d'):<15} | "
                            f"🕐 {next_candle_time.strftime('%H:%M:%S'):<12} | {signal_display:<14} | "
                            f"{conf_display:<14} | 💰 {price:<12.5f} | 🔧 {strategy_name:<15}"
                        )

                        print_colored(row_line, signal_color)

                    # Display bottom separator and status messages
                    print_colored("=" * 120, "SKY_BLUE")

                    # Display status messages based on signals found
                    if signals_found:
                        print_colored("✅ Trading signals found", "LIST_GREEN", bold=True)
                        print_colored("📊 Signals found in this scan cycle", "LIST_GREEN")
                    else:
                        print_colored("❌ No trading signals found", "HOLD", bold=True)
                        print_colored("📊 No signals found in this scan cycle", "HOLD")

                    # Calculate processing time
                    processing_time = (datetime.now() - signal_start_time).total_seconds()
                    last_signal_time = next_candle_time

                    print_colored(f"⏳ Processing took {processing_time:.2f}s.", "SKY_BLUE")
                    print_colored(f"🚀 Scan completed in {processing_time:.2f}s", "LIST_GREEN")

                    # Calculate next scan time
                    wait_time = max(10, duration - 2)  # Wait for next candle minus 2 seconds
                    next_scan_time = datetime.now() + timedelta(seconds=wait_time)
                    print_colored(f"⏳ Next scan in {int(wait_time)} seconds at {next_scan_time.strftime('%H:%M:%S')}", "SKY_BLUE")

                    # Simple wait without countdown
                    await asyncio.sleep(wait_time)
                else:
                    # Already processed this candle, wait a bit
                    await asyncio.sleep(0.5)
            else:
                # Wait until 2 seconds before next candle
                wait_time = max(0.5, time_to_next - 2)
                await asyncio.sleep(min(wait_time, 10))  # Max 10 second waits

    except KeyboardInterrupt:
        print_colored("\n" + "=" * 80, "SKY_BLUE")
        print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "HOLD", bold=True)
        print_colored("🎉 Hope your session was productive and successful!", "LIST_GREEN", bold=True)
        print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE")
        print_colored("=" * 80, "SKY_BLUE")
    except Exception as e:
        print_colored(f"\n❌ Trading bot error: {str(e)}", "ERROR")
    finally:
        if quotex_client:
            quotex_client.close()

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Select option (1-5): ").strip()

            if choice == '1':
                print_colored("⚠️  Practice mode selected", "WARNING")
                asyncio.run(run_trading_bot("PRACTICE", is_practice_only=True))
            elif choice == '2':
                print_colored("⚠️  Demo trading mode selected", "WARNING")
                asyncio.run(run_trading_bot("PRACTICE"))
            elif choice == '3':
                print_colored("🚨 Live trading mode selected", "SKY_BLUE", bold=True)
                confirm = input("⚠️  Are you sure you want to trade with real money? (yes/no): ").strip().lower()
                if confirm == 'yes':
                    asyncio.run(run_trading_bot("REAL"))
                else:
                    print_colored("❌ Live trading cancelled", "WARNING")
            elif choice == '4':
                asyncio.run(check_quotex_balance())
            elif choice == '5':
                print_colored("=" * 80, "SKY_BLUE")
                print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "HOLD", bold=True)
                print_colored("🎉 Hope your session was productive and successful!", "LIST_GREEN", bold=True)
                print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE")
                print_colored("=" * 80, "SKY_BLUE")
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-5.", "ERROR")

            # Wait for user to continue
            if choice in ['1', '2', '3', '4']:
                print()
                input("Press Enter to continue...")
                print()

        except KeyboardInterrupt:
            print_colored("\n👋 Goodbye! Session ended by user", "WARNING")
            break
        except Exception as e:
            print_colored(f"❌ Unexpected error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
